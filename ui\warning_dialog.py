"""
نافذة تحذير موحدة للاستخدام في جميع أقسام البرنامج
"""

from PyQt5.QtWidgets import QDialog, QVBoxLayout, QHBoxLayout, QLabel, QPushButton, QFrame
from PyQt5.QtCore import Qt
from PyQt5.QtGui import QFont
import ctypes
from ctypes import wintypes


class WarningDialog(QDialog):
    """نافذة تحذير متطورة مشابهة لنوافذ البرنامج"""

    def __init__(self, parent=None, message=""):
        super().__init__(parent)
        self.message = message
        self.parent_widget = parent
        self.setup_ui()

    def setup_ui(self):
        """إعداد واجهة النافذة مطابقة لنوافذ البرنامج"""
        # عنوان النافذة
        self.setWindowTitle("⚠️ تحذير - نظام إدارة العملاء المتطور والشامل")
        
        # إزالة علامة الاستفهام وتحسين شريط العنوان
        self.setWindowFlags(Qt.Dialog | Qt.WindowTitleHint | Qt.WindowCloseButtonHint)
        
        # تخصيص شريط العنوان
        self.customize_title_bar()
        
        self.setModal(True)
        self.resize(400, 200)
        
        # تطبيق التصميم العام
        self.setStyleSheet("""
            QDialog {
                background: qlineargradient(x1:0, y1:0, x2:1, y2:1,
                    stop:0 rgba(30, 41, 59, 0.95),
                    stop:0.3 rgba(51, 65, 85, 0.9),
                    stop:0.7 rgba(71, 85, 105, 0.9),
                    stop:1 rgba(30, 41, 59, 0.95));
                border: 3px solid rgba(96, 165, 250, 0.8);
                border-radius: 15px;
            }
        """)
        
        # التخطيط الرئيسي
        main_layout = QVBoxLayout()
        main_layout.setContentsMargins(20, 20, 20, 20)
        main_layout.setSpacing(20)
        
        # إطار المحتوى
        content_frame = QFrame()
        content_frame.setStyleSheet("""
            QFrame {
                background: qlineargradient(x1:0, y1:0, x2:1, y2:1,
                    stop:0 rgba(248, 250, 252, 0.95),
                    stop:0.5 rgba(241, 245, 249, 0.9),
                    stop:1 rgba(226, 232, 240, 0.85));
                border: 2px solid rgba(96, 165, 250, 0.6);
                border-radius: 10px;
                padding: 15px;
            }
        """)
        
        content_layout = QVBoxLayout(content_frame)
        content_layout.setContentsMargins(15, 15, 15, 15)
        content_layout.setSpacing(15)
        
        # رسالة التحذير
        message_label = QLabel(self.message)
        message_label.setAlignment(Qt.AlignCenter)
        message_label.setWordWrap(True)
        message_label.setStyleSheet("""
            QLabel {
                color: #1f2937;
                font-size: 16px;
                font-weight: bold;
                background: transparent;
                border: none;
                padding: 10px;
            }
        """)
        
        content_layout.addWidget(message_label)
        main_layout.addWidget(content_frame)
        
        # إطار الأزرار
        button_frame = QFrame()
        button_frame.setStyleSheet("""
            QFrame {
                background: transparent;
                border: none;
            }
        """)
        
        button_layout = QHBoxLayout(button_frame)
        button_layout.setContentsMargins(0, 0, 0, 0)
        button_layout.setSpacing(15)
        
        # زر موافق
        ok_button = QPushButton("موافق")
        ok_button.clicked.connect(self.accept)
        ok_button.setStyleSheet("""
            QPushButton {
                background: qlineargradient(x1:0, y1:0, x2:1, y2:1,
                    stop:0 #3b82f6, stop:1 #1d4ed8);
                color: white;
                border: 3px solid #2563eb;
                border-radius: 12px;
                padding: 12px 30px;
                font-size: 14px;
                font-weight: bold;
                text-transform: uppercase;
                letter-spacing: 1px;
                box-shadow: 0 4px 15px rgba(59, 130, 246, 0.4);
                text-shadow: 0 1px 2px rgba(0, 0, 0, 0.3);
            }
            QPushButton:hover {
                background: qlineargradient(x1:0, y1:0, x2:1, y2:1,
                    stop:0 #2563eb, stop:1 #1e40af);
                border: 3px solid #1d4ed8;
                box-shadow: 0 6px 20px rgba(59, 130, 246, 0.6);
                transform: translateY(-2px);
            }
            QPushButton:pressed {
                background: qlineargradient(x1:0, y1:0, x2:1, y2:1,
                    stop:0 #1d4ed8, stop:1 #1e3a8a);
                box-shadow: 0 2px 8px rgba(59, 130, 246, 0.3);
                transform: translateY(0px);
            }
        """)
        
        button_layout.addStretch()
        button_layout.addWidget(ok_button)
        button_layout.addStretch()
        
        main_layout.addWidget(button_frame)
        self.setLayout(main_layout)

    def customize_title_bar(self):
        """تخصيص شريط العنوان مطابق لنوافذ البرنامج"""
        try:
            # الحصول على معرف النافذة
            hwnd = int(self.winId())
            
            # تعيين لون خلفية شريط العنوان (أزرق داكن)
            DWMWA_CAPTION_COLOR = 35
            caption_color = 0x00593A1E  # لون أزرق داكن بتنسيق BGR
            
            ctypes.windll.dwmapi.DwmSetWindowAttribute(
                hwnd,
                DWMWA_CAPTION_COLOR,
                ctypes.byref(wintypes.DWORD(caption_color)),
                ctypes.sizeof(wintypes.DWORD)
            )
            
            # تعيين لون النص أبيض
            DWMWA_TEXT_COLOR = 36
            text_color = 0x00FFFFFF  # أبيض بتنسيق BGR
            
            ctypes.windll.dwmapi.DwmSetWindowAttribute(
                hwnd,
                DWMWA_TEXT_COLOR,
                ctypes.byref(wintypes.DWORD(text_color)),
                ctypes.sizeof(wintypes.DWORD)
            )
            
        except Exception as e:
            print(f"تحذير: فشل في تطبيق تصميم شريط العنوان: {e}")
